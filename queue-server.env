# Queue Server .env (Dedicated queue processing server)
APP_NAME=Laravel-Queue-Server
APP_ENV=production
APP_KEY=your-app-key
APP_DEBUG=false

# Database (Same as web instances)
DB_CONNECTION=mysql
DB_HOST=shared-database-server
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Queue Configuration
QUEUE_CONNECTION=database
# OR if using Redis:
# QUEUE_CONNECTION=redis
# REDIS_HOST=shared-redis-server

# Cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# This server ONLY processes queues
# No web traffic should come here
