# Web Instance .env (Clone instances)
APP_NAME=Laravel
APP_ENV=production
APP_KEY=your-app-key
APP_DEBUG=false
APP_URL=http://your-domain.com

# Database (Shared)
DB_CONNECTION=mysql
DB_HOST=shared-database-server
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Queue Configuration (Important!)
QUEUE_CONNECTION=database
# OR if using Redis:
# QUEUE_CONNECTION=redis
# REDIS_HOST=shared-redis-server
# REDIS_PASSWORD=null
# REDIS_PORT=6379

# Cache (Shared)
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# Mail
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025

# DO NOT RUN QUEUE WORKERS ON WEB INSTANCES
# Queue workers should only run on dedicated server
