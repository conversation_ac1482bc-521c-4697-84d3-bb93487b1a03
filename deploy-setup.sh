#!/bin/bash

# Deployment script for Load Balanced Laravel with Supervisor

echo "Setting up Load Balanced Laravel Environment..."

# Function to setup web instance (clone)
setup_web_instance() {
    echo "Setting up Web Instance..."
    
    # Copy application files
    rsync -av --exclude='storage/logs' --exclude='storage/framework/cache' /path/to/laravel/ /var/www/html/
    
    # Set permissions
    chown -R www-data:www-data /var/www/html/
    chmod -R 755 /var/www/html/
    chmod -R 775 /var/www/html/storage
    chmod -R 775 /var/www/html/bootstrap/cache
    
    # Copy web instance environment
    cp web-instance.env /var/www/html/.env
    
    # Install dependencies
    cd /var/www/html/
    composer install --no-dev --optimize-autoloader
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    # DO NOT start queue workers on web instances
    echo "Web instance setup complete - NO queue workers"
}

# Function to setup queue server
setup_queue_server() {
    echo "Setting up Queue Server..."
    
    # Copy application files
    rsync -av /path/to/laravel/ /var/www/queue-server/
    
    # Set permissions
    chown -R www-data:www-data /var/www/queue-server/
    
    # Copy queue server environment
    cp queue-server.env /var/www/queue-server/.env
    
    # Install dependencies
    cd /var/www/queue-server/
    composer install --no-dev --optimize-autoloader
    php artisan config:cache
    
    # Setup supervisor for queue workers
    cp supervisor-queue-worker.conf /etc/supervisor/conf.d/
    
    # Update supervisor configuration with correct path
    sed -i 's|/path/to/your/laravel|/var/www/queue-server|g' /etc/supervisor/conf.d/supervisor-queue-worker.conf
    
    # Restart supervisor
    supervisorctl reread
    supervisorctl update
    supervisorctl start all
    
    echo "Queue server setup complete with supervisor"
}

# Function to setup load balancer
setup_load_balancer() {
    echo "Setting up Load Balancer..."
    
    # Copy nginx configuration
    cp nginx-load-balancer.conf /etc/nginx/sites-available/laravel-lb
    ln -sf /etc/nginx/sites-available/laravel-lb /etc/nginx/sites-enabled/
    
    # Remove default site
    rm -f /etc/nginx/sites-enabled/default
    
    # Test and reload nginx
    nginx -t && systemctl reload nginx
    
    echo "Load balancer setup complete"
}

# Main execution
case "$1" in
    "web")
        setup_web_instance
        ;;
    "queue")
        setup_queue_server
        ;;
    "lb")
        setup_load_balancer
        ;;
    *)
        echo "Usage: $0 {web|queue|lb}"
        echo "  web   - Setup web instance (clone)"
        echo "  queue - Setup queue processing server"
        echo "  lb    - Setup load balancer"
        exit 1
        ;;
esac
