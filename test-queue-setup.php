<?php
// Test script to verify queue setup

// Test 1: Dispatch test jobs
echo "Dispatching test jobs...\n";

for ($i = 1; $i <= 10; $i++) {
    \App\Jobs\TestJob::dispatch("Test job #$i");
    echo "Dispatched job #$i\n";
}

// Test 2: Check queue status
echo "\nChecking queue status...\n";
$pendingJobs = \DB::table('jobs')->count();
echo "Pending jobs: $pendingJobs\n";

$failedJobs = \DB::table('failed_jobs')->count();
echo "Failed jobs: $failedJobs\n";

// Test 3: Monitor queue workers
echo "\nQueue worker processes:\n";
exec('ps aux | grep "queue:work"', $output);
foreach ($output as $line) {
    if (strpos($line, 'grep') === false) {
        echo $line . "\n";
    }
}

// Test 4: Check supervisor status
echo "\nSupervisor status:\n";
exec('supervisorctl status', $supervisorOutput);
foreach ($supervisorOutput as $line) {
    echo $line . "\n";
}

// Test Job Class
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $message;

    public function __construct($message)
    {
        $this->message = $message;
    }

    public function handle()
    {
        Log::info("Processing: " . $this->message);
        sleep(2); // Simulate work
        Log::info("Completed: " . $this->message);
    }
}
